name: Bug Report
description: You think something is broken in the UI
title: "[Bug]: "
labels: ["bug-report"]

body:
  - type: markdown
    attributes:
      value: |
        > The title of the bug report should be short and descriptive.
        > Use relevant keywords for searchability.
        > Do not leave it blank, but also do not put an entire error log in it.
  - type: checkboxes
    attributes:
      label: Checklist
      description: |
        Please perform basic debugging to see if extensions or configuration is the cause of the issue.
        Basic debug procedure
        　1. Disable all third-party extensions - check if extension is the cause
        　2. Update extensions and webui - sometimes things just need to be updated
        　3. Backup and remove your config.json and ui-config.json - check if the issue is caused by bad configuration
        　4. Delete venv with third-party extensions disabled - sometimes extensions might cause wrong libraries to be installed
        　5. Try a fresh installation webui in a different directory - see if a clean installation solves the issue
        Before making a issue report please, check that the issue hasn't been reported recently.
      options:
        - label: The issue exists after disabling all extensions
        - label: The issue exists on a clean installation of webui
        - label: The issue is caused by an extension, but I believe it is caused by a bug in the webui
        - label: The issue exists in the current version of the webui
        - label: The issue has not been reported before recently
        - label: The issue has been reported before but has not been fixed yet
  - type: markdown
    attributes:
      value: |
        > Please fill this form with as much information as possible. Don't forget to "Upload Sysinfo" and "What browsers" and provide screenshots if possible
  - type: textarea
    id: what-did
    attributes:
      label: What happened?
      description: Tell us what happened in a very clear and simple way
      placeholder: |
        txt2img is not working as intended.
    validations:
      required: true
  - type: textarea
    id: steps
    attributes:
      label: Steps to reproduce the problem
      description: Please provide us with precise step by step instructions on how to reproduce the bug
      placeholder: |
        1. Go to ...
        2. Press ...
        3. ...
    validations:
      required: true
  - type: textarea
    id: what-should
    attributes:
      label: What should have happened?
      description: Tell us what you think the normal behavior should be
      placeholder: |
        WebUI should ...
    validations:
      required: true
  - type: dropdown
    id: browsers
    attributes:
      label: What browsers do you use to access the UI ?
      multiple: true
      options:
        - Mozilla Firefox
        - Google Chrome
        - Brave
        - Apple Safari
        - Microsoft Edge
        - Android
        - iOS
        - Other
  - type: textarea
    id: sysinfo
    attributes:
      label: Sysinfo
      description: System info file, generated by WebUI. You can generate it in settings, on the Sysinfo page. Drag the file into the field to upload it. If you submit your report without including the sysinfo file, the report will be closed. If needed, review the report to make sure it includes no personal information you don't want to share. If you can't start WebUI, you can use --dump-sysinfo commandline argument to generate the file.
      placeholder: |
        1. Go to WebUI Settings -> Sysinfo -> Download system info.
            If WebUI fails to launch, use --dump-sysinfo commandline argument to generate the file
        2. Upload the Sysinfo as a attached file, Do NOT paste it in as plain text.
    validations:
      required: true
  - type: textarea
    id: logs
    attributes:
      label: Console logs
      description: Please provide **full** cmd/terminal logs from the moment you started UI to the end of it, after the bug occurred. If it's very long, provide a link to pastebin or similar service.
      render: Shell
    validations:
      required: true
  - type: textarea
    id: misc
    attributes:
      label: Additional information
      description: | 
        Please provide us with any relevant additional info or context.
        Examples:
        　I have updated my GPU driver recently.
