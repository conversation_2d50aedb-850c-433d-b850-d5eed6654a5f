/* temporary fix to load default gradio font in frontend instead of backend */

@import url('webui-assets/css/sourcesanspro.css');


/* temporary fix to hide gradio crop tool until it's fixed https://github.com/gradio-app/gradio/issues/3810 */

div.gradio-image button[aria-label="Edit"] {
    display: none;
}


/* general gradio fixes */

:root, .dark{
    --checkbox-label-gap: 0.25em 0.1em;
    --section-header-text-size: 12pt;
    --block-background-fill: transparent;

}

.block.padded:not(.gradio-accordion) {
    padding: 0 !important;
}

div.gradio-container{
    max-width: unset !important;
}

.hidden{
    display: none !important;
}

.compact{
    background: transparent !important;
    padding: 0 !important;
}

div.form{
    border-width: 0;
    box-shadow: none;
    background: transparent;
    overflow: visible;
    gap: 0.5em;
}

.block.gradio-dropdown,
.block.gradio-slider,
.block.gradio-checkbox,
.block.gradio-textbox,
.block.gradio-radio,
.block.gradio-checkboxgroup,
.block.gradio-number,
.block.gradio-colorpicker {
    border-width: 0 !important;
    box-shadow: none !important;
}

div.gradio-group, div.styler{
    border-width: 0 !important;
    background: none;
}
.gap.compact{
    padding: 0;
    gap: 0.2em 0;
}

div.compact{
    gap: 1em;
}

.gradio-dropdown label span:not(.has-info),
.gradio-textbox label span:not(.has-info),
.gradio-number label span:not(.has-info)
{
    margin-bottom: 0;
}

.gradio-dropdown ul.options{
    z-index: 3000;
    min-width: fit-content;
    max-width: inherit;
    white-space: nowrap;
}

@media (pointer:fine) {
    .gradio-dropdown ul.options li.item {
        padding: 0.05em 0;
    }
}

.gradio-dropdown ul.options li.item.selected {
    background-color: var(--neutral-100);
}

.dark .gradio-dropdown ul.options li.item.selected {
    background-color: var(--neutral-900);
}

.gradio-dropdown div.wrap.wrap.wrap.wrap{
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.gradio-dropdown:not(.multiselect) .wrap-inner.wrap-inner.wrap-inner{
    flex-wrap: unset;
}

.gradio-dropdown .single-select{
    white-space: nowrap;
    overflow: hidden;
}

.gradio-dropdown .token-remove.remove-all.remove-all{
    display: none;
}

.gradio-dropdown.multiselect .token-remove.remove-all.remove-all{
    display: flex;
}

.gradio-slider input[type="number"]{
    width: 6em;
}

.block.gradio-checkbox {
    margin: 0.75em 1.5em 0 0;
}

.gradio-html div.wrap{
    height: 100%;
}
div.gradio-html.min{
    min-height: 0;
}

.block.gradio-gallery{
    background: var(--input-background-fill);
}

.gradio-container .prose a, .gradio-container .prose a:visited{
    color: unset;
    text-decoration: none;
}

a{
    font-weight: bold;
    cursor: pointer;
}

/* gradio 3.39 puts a lot of overflow: hidden all over the place for an unknown reason. */
div.gradio-container, .block.gradio-textbox, div.gradio-group, div.gradio-dropdown{
    overflow: visible !important;
}

/* align-items isn't enough and elements may overflow in Safari. */
.unequal-height {
    align-content: flex-start;
}


/* general styled components */

.gradio-button.tool{
    max-width: 2.2em;
    min-width: 2.2em !important;
    height: 2.4em;
    align-self: end;
    line-height: 1em;
    border-radius: 0.5em;
}

.gradio-button.secondary-down{
    background: var(--button-secondary-background-fill);
    color: var(--button-secondary-text-color);
}
.gradio-button.secondary-down, .gradio-button.secondary-down:hover{
    box-shadow: 1px 1px 1px rgba(0,0,0,0.25) inset, 0px 0px 3px rgba(0,0,0,0.15) inset;
}
.gradio-button.secondary-down:hover{
    background: var(--button-secondary-background-fill-hover);
    color: var(--button-secondary-text-color-hover);
}

button.custom-button{
    border-radius: var(--button-large-radius);
    padding: var(--button-large-padding);
    font-weight: var(--button-large-text-weight);
    border: var(--button-border-width) solid var(--button-secondary-border-color);
    background: var(--button-secondary-background-fill);
    color: var(--button-secondary-text-color);
    font-size: var(--button-large-text-size);
    display: inline-flex;
    justify-content: center;
    align-items: center;
    transition: var(--button-transition);
    box-shadow: var(--button-shadow);
    text-align: center;
}

div.block.gradio-accordion {
    border: 1px solid var(--block-border-color) !important;
    border-radius: 8px !important;
    margin: 2px 0;
    padding: 8px 8px;
}

input[type="checkbox"].input-accordion-checkbox{
    vertical-align: sub;
    margin-right: 0.5em;
}


/* txt2img/img2img specific */

.block.token-counter{
    position: absolute;
    display: inline-block;
    right: 1em;
    min-width: 0 !important;
    width: auto;
    z-index: 100;
    top: -0.75em;
}

.block.token-counter-visible{
    display: block !important;
}

.block.token-counter span{
    background: var(--input-background-fill) !important;
    box-shadow: 0 0 0.0 0.3em rgba(192,192,192,0.15), inset 0 0 0.6em rgba(192,192,192,0.075);
    border: 2px solid rgba(192,192,192,0.4) !important;
    border-radius: 0.4em;
}

.block.token-counter.error span{
    box-shadow: 0 0 0.0 0.3em rgba(255,0,0,0.15), inset 0 0 0.6em rgba(255,0,0,0.075);
    border: 2px solid rgba(255,0,0,0.4) !important;
}

.block.token-counter div{
    display: inline;
}

.block.token-counter span{
    padding: 0.1em 0.75em;
}

[id$=_subseed_show]{
    min-width: auto !important;
    flex-grow: 0 !important;
    display: flex;
}

[id$=_subseed_show] label{
    margin-bottom: 0.65em;
    align-self: end;
}

[id$=_seed_extras] > div{
    gap: 0.5em;
}

.html-log .comments{
    padding-top: 0.5em;
}

.html-log .comments:empty{
    padding-top: 0;
}

.html-log .performance {
    font-size: 0.85em;
    color: #444;
    display: flex;
}

.html-log .performance p{
    display: inline-block;
}

.html-log .performance p.time, .performance p.vram, .performance p.profile, .performance p.time abbr, .performance p.vram abbr {
    margin-bottom: 0;
    color: var(--block-title-text-color);
}

.html-log .performance p.time {
}

.html-log .performance p.vram {
    margin-left: auto;
}

.html-log .performance p.profile {
    margin-left: 0.5em;
}

.html-log .performance .measurement{
    color: var(--body-text-color);
    font-weight: bold;
}

#txt2img_generate, #img2img_generate {
    min-height: 4.5em;
}

#txt2img_generate, #img2img_generate {
    min-height: 4.5em;
}
.generate-box-compact #txt2img_generate, .generate-box-compact #img2img_generate {
    min-height: 3em;
}

@media screen and (min-width: 2500px) {
    #txt2img_gallery, #img2img_gallery {
        min-height: 768px;
    }
}

.gradio-gallery .thumbnails img {
    object-fit: scale-down !important;
}
#txt2img_actions_column, #img2img_actions_column {
    gap: 0.5em;
}
#txt2img_tools, #img2img_tools{
    gap: 0.4em;
}

.interrogate-col{
    min-width: 0 !important;
    max-width: fit-content;
    gap: 0.5em;
}
.interrogate-col > button{
    flex: 1;
}

.generate-box{
    position: relative;
}
.gradio-button.generate-box-skip, .gradio-button.generate-box-interrupt, .gradio-button.generate-box-interrupting{
    position: absolute;
    width: 50%;
    height: 100%;
    display: none;
    background: #b4c0cc;
}
.gradio-button.generate-box-skip:hover, .gradio-button.generate-box-interrupt:hover, .gradio-button.generate-box-interrupting:hover{
    background: #c2cfdb;
}
.gradio-button.generate-box-interrupt, .gradio-button.generate-box-interrupting{
    left: 0;
    border-radius: 0.5rem 0 0 0.5rem;
}
.gradio-button.generate-box-skip{
    right: 0;
    border-radius: 0 0.5rem 0.5rem 0;
}

#img2img_scale_resolution_preview.block{
    display: flex;
    align-items: end;
}

#txtimg_hr_finalres .resolution, #img2img_scale_resolution_preview .resolution{
    font-weight: bold;
}

#txtimg_hr_finalres div.pending, #img2img_scale_resolution_preview div.pending {
    opacity: 1;
    transition: opacity 0s;
}

.inactive{
    opacity: 0.5;
}

[id$=_column_batch]{
    min-width: min(13.5em, 100%) !important;
}

div.dimensions-tools{
    min-width: 1.6em !important;
    max-width: fit-content;
    flex-direction: column;
    place-content: center;
}

div#extras_scale_to_tab div.form{
    flex-direction: row;
}

#img2img_sketch, #img2maskimg, #inpaint_sketch {
    overflow: overlay !important;
    resize: auto;
    background: var(--panel-background-fill);
    z-index: 5;
}

.image-buttons > .form{
    justify-content: center;
}

.infotext {
    overflow-wrap: break-word;
}

#img2img_column_batch{
    align-self: end;
    margin-bottom: 0.9em;
}

#img2img_unused_scale_by_slider{
    visibility: hidden;
    width: 0.5em;
    max-width: 0.5em;
    min-width: 0.5em;
}

div.toprow-compact-stylerow{
    margin: 0.5em 0;
}

div.toprow-compact-tools{
    min-width: fit-content !important;
    max-width: fit-content;
}

/* settings */
#quicksettings {
    align-items: end;
}

#quicksettings > div, #quicksettings > fieldset{
    max-width: 36em;
    width: fit-content;
    flex: 0 1 fit-content;
    padding: 0;
    border: none;
    box-shadow: none;
    background: none;
}
#quicksettings > div.gradio-dropdown{
    min-width: 24em !important;
}

#settings{
    display: block;
}

#settings > div{
    border: none;
    margin-left: 10em;
    padding: 0 var(--spacing-xl);
}

#settings > div.tab-nav{
    float: left;
    display: block;
    margin-left: 0;
    width: 10em;
}

#settings > div.tab-nav button{
    display: block;
    border: none;
    text-align: left;
    white-space: initial;
    padding: 4px;
}

#settings > div.tab-nav .settings-category{
    display: block;
    margin: 1em 0 0.25em 0;
    font-weight: bold;
    text-decoration: underline;
    cursor: default;
    user-select: none;
}

#settings_result{
    height: 1.4em;
    margin: 0 1.2em;
}

table.popup-table{
    background: var(--body-background-fill);
    color: var(--body-text-color);
    border-collapse: collapse;
    margin: 1em;
    border: 4px solid var(--body-background-fill);
}

table.popup-table td{
    padding: 0.4em;
    border: 1px solid rgba(128, 128, 128, 0.5);
    max-width: 36em;
}

table.popup-table .muted{
    color: #aaa;
}

table.popup-table .link{
    text-decoration: underline;
    cursor: pointer;
    font-weight: bold;
}

.ui-defaults-none{
    color: #aaa !important;
}

#settings span{
    color: var(--body-text-color);
}

#settings .gradio-textbox, #settings .gradio-slider, #settings .gradio-number, #settings .gradio-dropdown, #settings .gradio-checkboxgroup, #settings .gradio-radio{
    margin-top: 0.75em;
}

#settings span .settings-comment {
    display: inline
}

.settings-comment a{
    text-decoration: underline;
}

.settings-comment .info{
    opacity: 0.75;
}

.settings-comment .info ol{
    margin: 0.4em 0 0.8em 1em;
}

#sysinfo_download a.sysinfo_big_link{
    font-size: 24pt;
}

#sysinfo_download a{
    text-decoration: underline;
}

#sysinfo_validity{
    font-size: 18pt;
}

#settings .settings-info{
    max-width: 48em;
    border: 1px dotted #777;
    margin: 0;
    padding: 1em;
}


/* live preview */
.progressDiv{
    position: absolute;
    height: 20px;
    background: #b4c0cc;
    border-radius: 3px !important;
    top: -14px;
    left: 0px;
    width: 100%;
}

.progress-container{
    position: relative;
}

[id$=_results].mobile{
    margin-top: 28px;
}

.dark .progressDiv{
    background: #424c5b;
}

.progressDiv .progress{
    width: 0%;
    height: 20px;
    background: #0060df;
    color: white;
    font-weight: bold;
    line-height: 20px;
    padding: 0 8px 0 0;
    text-align: right;
    border-radius: 3px;
    overflow: visible;
    white-space: nowrap;
    padding: 0 0.5em;
}

.livePreview{
    position: absolute;
    z-index: 300;
    background: var(--background-fill-primary);
    width: 100%;
    height: 100%;
}

.livePreview img{
    position: absolute;
    object-fit: contain;
    width: 100%;
    height: calc(100% - 60px);  /* to match gradio's height */
}

/* fullscreen popup (ie in Lora's (i) button) */

.popup-metadata{
    color: black;
    background: white;
    display: inline-block;
    padding: 1em;
    white-space: pre-wrap;
}

.global-popup{
    display: flex;
    position: fixed;
    z-index: 1001;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
}

.global-popup *{
    box-sizing: border-box;
}

.global-popup-close:before {
    content: "×";
    position: fixed;
    right: 0.25em;
    top: 0;
    cursor: pointer;
    color: white;
    font-size: 32pt;
}

.global-popup-close{
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(20, 20, 20, 0.95);
}

.global-popup-inner{
    display: inline-block;
    margin: auto;
    padding: 2em;
    z-index: 1001;
    max-height: 90%;
    max-width: 90%;
}

/* fullpage image viewer */

#lightboxModal{
    display: none;
    position: fixed;
    z-index: 1001;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(20, 20, 20, 0.95);
    user-select: none;
    -webkit-user-select: none;
    flex-direction: column;
}

.modalControls {
    display: flex;
    position: absolute;
    right: 0px;
    left: 0px;
    gap: 1em;
    padding: 1em;
    background-color:rgba(0,0,0,0);
    z-index: 1;
    transition: 0.2s ease background-color;
}
.modalControls:hover {
    background-color:rgba(0,0,0, var(--sd-webui-modal-lightbox-toolbar-opacity));
}
.modalClose {
    margin-left: auto;
}
.modalControls span{
    color: white;
    text-shadow: 0px 0px 0.25em black;
    font-size: 35px;
    font-weight: bold;
    cursor: pointer;
    width: 1em;
}

.modalControls span:hover, .modalControls span:focus{
    color: #999;
    text-decoration: none;
}

#lightboxModal > img {
    display: block;
    margin: auto;
    width: auto;
}

#lightboxModal > img.modalImageFullscreen{
    object-fit: contain;
    height: 100%;
    width: 100%;
    min-height: 0;
}

.modalPrev,
.modalNext {
  cursor: pointer;
  position: absolute;
  top: 50%;
  width: auto;
  padding: 16px;
  margin-top: -50px;
  color: white;
  font-weight: bold;
  font-size: 20px;
  transition: 0.6s ease;
  border-radius: 0 3px 3px 0;
  user-select: none;
  -webkit-user-select: none;
}

.modalNext {
  right: 0;
  border-radius: 3px 0 0 3px;
}

.modalPrev:hover,
.modalNext:hover {
  background-color: rgba(0, 0, 0, 0.8);
}

#imageARPreview {
    position: absolute;
    top: 0px;
    left: 0px;
    border: 2px solid red;
    background: rgba(255, 0, 0, 0.3);
    z-index: 900;
    pointer-events: none;
    display: none;
}

@media (pointer: fine) {
    .modalPrev:hover,
    .modalNext:hover,
    .modalControls:hover ~ .modalPrev,
    .modalControls:hover ~ .modalNext,
    .modalControls:hover .cursor {
        opacity: 1;
    }

    .modalPrev,
    .modalNext,
    .modalControls .cursor {
        opacity: var(--sd-webui-modal-lightbox-icon-opacity);
    }
}

/* context menu (ie for the generate button) */

#context-menu{
    z-index:9999;
    position:absolute;
    display:block;
    padding:0px 0;
    border:2px solid var(--primary-800);
    border-radius:8px;
    box-shadow:1px 1px 2px var(--primary-500);
    width: 200px;
}

.context-menu-items{
    list-style: none;
    margin: 0;
    padding: 0;
}

.context-menu-items a{
    display:block;
    padding:5px;
    cursor:pointer;
}

.context-menu-items a:hover{
    background: var(--primary-700);
}


/* extensions */

#tab_extensions table{
    border-collapse: collapse;
    overflow-x: auto;
    display: block;
}

#tab_extensions table td, #tab_extensions table th{
    border: 1px solid #ccc;
    padding: 0.25em 0.5em;
}

#tab_extensions table input[type="checkbox"]{
    margin-right: 0.5em;
    appearance: checkbox;
}

#tab_extensions button{
    max-width: 16em;
}

#tab_extensions input[disabled="disabled"]{
    opacity: 0.5;
}

.extension-tag{
    font-weight: bold;
    font-size: 95%;
}

#available_extensions .info{
    margin: 0;
}

#available_extensions .info{
    margin: 0.5em 0;
    display: flex;
    margin-top: auto;
    opacity: 0.80;
    font-size: 90%;
}

#available_extensions .date_added{
    margin-right: auto;
    display: inline-block;
}

#available_extensions .star_count{
    margin-left: auto;
    display: inline-block;
}

.compact-checkbox-group  div label {
    padding: 0.1em 0.3em !important;
}

/* extensions tab table row hover highlight */

#extensions tr:hover td,
#config_state_extensions tr:hover td,
#available_extensions tr:hover td {
    background: rgba(0, 0, 0, 0.15);
}

.dark #extensions tr:hover td ,
.dark #config_state_extensions tr:hover td ,
.dark #available_extensions tr:hover td {
    background: rgba(255, 255, 255, 0.15);
}

/* replace original footer with ours */

footer {
    display: none !important;
}

#footer{
    text-align: center;
}

#footer div{
    display: inline-block;
}

#footer .versions{
    font-size: 85%;
    opacity: 0.85;
}

/* extra networks UI */

.extra-page > div.gap{
    gap: 0;
}

.extra-page-prompts{
    margin-bottom: 0;
}

.extra-page-prompts.extra-page-prompts-active{
    margin-bottom: 1em;
}

.extra-networks > div.tab-nav{
    min-height: 2.7rem;
}

.extra-networks-controls-div{
    align-self: center;
    margin-left: auto;
}

.extra-networks > div > [id *= '_extra_']{
    margin: 0.3em;
}

.extra-networks .tab-nav .search,
.extra-networks .tab-nav .sort
{
    margin: 0.3em;
    align-self: center;
    width: auto;
}

.extra-networks .tab-nav .search {
    width: 16em;
    max-width: 16em;
}

.extra-networks .tab-nav .sort {
    width: 12em;
    max-width: 12em;
}

#txt2img_extra_view, #img2img_extra_view {
    width: auto;
}

.extra-network-pane .nocards{
    margin: 1.25em 0.5em 0.5em 0.5em;
}

.extra-network-pane .nocards h1{
    font-size: 1.5em;
    margin-bottom: 1em;
}

.extra-network-pane .nocards li{
    margin-left: 0.5em;
}

.extra-network-pane .card .button-row{
    display: inline-flex;
    visibility: hidden;
    color: white;
}

.extra-network-pane .card .button-row {
    position: absolute;
    right: 0;
    z-index: 1;
}

.extra-network-pane .card:hover .button-row{
    visibility: visible;
}

.extra-network-pane .card-button{
    color: white;
}

.extra-network-pane .copy-path-button::before {
    content: "⎘";
}

.extra-network-pane .metadata-button::before{
    content: "🛈";
}

.extra-network-pane .edit-button::before{
    content: "🛠";
}

.extra-network-pane .card-button {
    width: 1.5em;
    text-shadow: 2px 2px 3px black;
    color: white;
    padding: 0.25em 0.1em;
}

.extra-network-pane .card-button:hover{
    color: red;
}

.extra-network-pane .card .card-button {
    font-size: 2rem;
}

.extra-network-pane .card-minimal .card-button {
    font-size: 1rem;
}

.standalone-card-preview.card .preview{
    position: absolute;
    object-fit: cover;
    width: 100%;
    height:100%;
}

.extra-network-pane .card, .standalone-card-preview.card{
    display: inline-block;
    margin: 0.5rem;
    width: 16rem;
    height: 24rem;
    box-shadow: 0 0 5px rgba(128, 128, 128, 0.5);
    border-radius: 0.2rem;
    position: relative;

    background-size: auto 100%;
    background-position: center;
    overflow: hidden;
    cursor: pointer;

    background-image: url('./file=html/card-no-preview.png')
}

.extra-network-pane .card:hover{
    box-shadow: 0 0 2px 0.3em rgba(0, 128, 255, 0.35);
}

.extra-network-pane .card .actions .additional{
    display: none;
}

.extra-network-pane .card .actions{
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 0.5em;
    background: rgba(0,0,0,0.5);
    box-shadow: 0 0 0.25em 0.25em rgba(0,0,0,0.5);
    text-shadow: 0 0 0.2em black;
}

.extra-network-pane .card .actions *{
    color: white;
}

.extra-network-pane .card .actions .name{
    font-size: 1.7em;
    font-weight: bold;
    line-break: anywhere;
}

.extra-network-pane .card .actions .description {
    display: block;
    max-height: 3em;
    white-space: pre-wrap;
    line-height: 1.1;
}

.extra-network-pane .card .actions .description:hover {
    max-height: none;
}

.extra-network-pane .card .actions:hover .additional{
    display: block;
}

.extra-network-pane .card ul{
    margin: 0.25em 0 0.75em 0.25em;
    cursor: unset;
}

.extra-network-pane .card ul a{
    cursor: pointer;
}

.extra-network-pane .card ul a:hover{
    color: red;
}

.extra-network-pane .card .preview{
    position: absolute;
    object-fit: cover;
    width: 100%;
    height:100%;
}

div.block.gradio-box.edit-user-metadata {
    width: 56em;
    background: var(--body-background-fill);
    padding: 2em !important;
}

.edit-user-metadata .extra-network-name{
    font-size: 18pt;
    color: var(--body-text-color);
}

.edit-user-metadata .file-metadata{
    color: var(--body-text-color);
}

.edit-user-metadata .file-metadata th{
    text-align: left;
}

.edit-user-metadata .file-metadata th, .edit-user-metadata .file-metadata td{
    padding: 0.3em 1em;
    overflow-wrap: anywhere;
    word-break: break-word;
}

.edit-user-metadata .wrap.translucent{
    background: var(--body-background-fill);
}
.edit-user-metadata .gradio-highlightedtext span{
    word-break: break-word;
}

.edit-user-metadata-buttons{
    margin-top: 1.5em;
}

div.block.gradio-box.popup-dialog, .popup-dialog {
    width: 56em;
    background: var(--body-background-fill);
    padding: 2em !important;
}

div.block.gradio-box.popup-dialog > div:last-child, .popup-dialog > div:last-child{
    margin-top: 1em;
}

div.block.input-accordion{

}

.input-accordion-extra{
    flex: 0 0 auto !important;
    margin: 0 0.5em 0 auto;
}

div.accordions > div.input-accordion{
    min-width: fit-content !important;
}

div.accordions > div.gradio-accordion .label-wrap span{
    white-space: nowrap;
    margin-right: 0.25em;
}

div.accordions{
    gap: 0.5em;
}

div.accordions > div.input-accordion.input-accordion-open{
    flex: 1 auto;
    flex-flow: column;
}


/* sticky right hand columns */

#img2img_results, #txt2img_results, #extras_results {
    position: sticky;
    top: 0.5em;
}

body.resizing {
    cursor: col-resize !important;
}

body.resizing * {
    pointer-events: none !important;
}

body.resizing .resize-handle {
    pointer-events: initial !important;
}

.resize-handle {
    position: relative;
    cursor: col-resize;
    grid-column: 2 / 3;
    min-width: 16px !important;
    max-width: 16px !important;
    height: 100%;
}

.resize-handle::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 7.5px;
    border-left: 1px dashed var(--border-color-primary);
}

/* ========================= */
.extra-network-pane {
    display: flex;
    height: calc(100vh - 24rem);
    resize: vertical;
    min-height: 52rem;
    flex-direction: column;
    overflow: hidden;
}

.extra-network-pane .extra-network-pane-content-dirs {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden;
}

.extra-network-pane .extra-network-pane-content-tree {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.extra-network-dirs-hidden .extra-network-dirs{ display: none; }
.extra-network-dirs-hidden .extra-network-tree{ display: none; }
.extra-network-dirs-hidden .resize-handle { display: none; }
.extra-network-dirs-hidden .resize-handle-row { display: flex !important; }

.extra-network-pane .extra-network-tree {
    flex: 1;
    font-size: 1rem;
    border: 1px solid var(--block-border-color);
    overflow: clip auto !important;
}

.extra-network-pane .extra-network-cards {
    flex: 3;
    overflow: clip auto !important;
    border: 1px solid var(--block-border-color);
}

.extra-network-pane .extra-network-tree .tree-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0;
    width: 100%;
    overflow: hidden;
}


.extra-network-pane .extra-network-cards::-webkit-scrollbar,
.extra-network-pane .extra-network-tree::-webkit-scrollbar {
    background-color: transparent;
    width: 16px;
}

.extra-network-pane .extra-network-cards::-webkit-scrollbar-track,
.extra-network-pane .extra-network-tree::-webkit-scrollbar-track {
    background-color: transparent;
    background-clip: content-box;
}

.extra-network-pane .extra-network-cards::-webkit-scrollbar-thumb,
.extra-network-pane .extra-network-tree::-webkit-scrollbar-thumb {
    background-color: var(--border-color-primary);
    border-radius: 16px;
    border: 4px solid var(--background-fill-primary);
}

.extra-network-pane .extra-network-cards::-webkit-scrollbar-button,
.extra-network-pane .extra-network-tree::-webkit-scrollbar-button {
    display: none;
}

.extra-network-control {
    position: relative;
    display: flex;
    width: 100%;
    padding: 0 !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    font-size: 1rem;
    text-align: left;
    user-select: none;
    background-color: transparent;
    border: none;
    transition: background 33.333ms linear;
    grid-template-rows: min-content;
    grid-template-columns: minmax(0, auto) repeat(4, min-content);
    grid-gap: 0.1rem;
    align-items: start;
}

.extra-network-control small{
    color: var(--input-placeholder-color);
    line-height: 2.2rem;
    margin: 0 0.5rem 0 0.75rem;
}

.extra-network-tree .tree-list--tree {}

/* Remove auto indentation from tree. Will be overridden later. */
.extra-network-tree .tree-list--subgroup {
    margin: 0 !important;
    padding: 0 !important;
    box-shadow: 0.5rem 0 0 var(--body-background-fill) inset,
                0.7rem 0 0 var(--neutral-800) inset;
}

/* Set indentation for each depth of tree. */
.extra-network-tree .tree-list--subgroup > .tree-list-item {
    margin-left: 0.4rem !important;
    padding-left: 0.4rem !important;
}

/* Styles for tree <li> elements. */
.extra-network-tree .tree-list-item {
    list-style: none;
    position: relative;
    background-color: transparent;
}

/* Directory <ul> visibility based on data-expanded attribute. */
.extra-network-tree .tree-list-content+.tree-list--subgroup {
    height: 0;
    visibility: hidden;
    opacity: 0;
}

.extra-network-tree .tree-list-content[data-expanded]+.tree-list--subgroup {
    height: auto;
    visibility: visible;
    opacity: 1;
}

/* File <li> */
.extra-network-tree .tree-list-item--subitem {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

/* <li> containing <ul> */
.extra-network-tree .tree-list-item--has-subitem {}

/* BUTTON ELEMENTS */
/* <button> */
.extra-network-tree .tree-list-content {
    position: relative;
    display: grid;
    width: 100%;
    padding: 0 !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    font-size: 1rem;
    text-align: left;
    user-select: none;
    background-color: transparent;
    border: none;
    transition: background 33.333ms linear;
    grid-template-rows: min-content;
    grid-template-areas: "leading-action leading-visual label trailing-visual trailing-action";
    grid-template-columns: min-content min-content minmax(0, auto) min-content min-content;
    grid-gap: 0.1rem;
    align-items: start;
    flex-grow: 1;
    flex-basis: 100%;
}
/* Buttons for directories. */
.extra-network-tree .tree-list-content-dir {}   

/* Buttons for files. */
.extra-network-tree .tree-list-item--has-subitem .tree-list--subgroup > li:first-child {
    padding-top: 0.5rem !important;
}

.dark .extra-network-tree div.tree-list-content:hover {
    -webkit-transition: all 0.05s ease-in-out;
	transition: all 0.05s ease-in-out;
    background-color: var(--neutral-800);
}

.dark .extra-network-tree div.tree-list-content[data-selected] {
    background-color: var(--neutral-700);
}

.extra-network-tree div.tree-list-content[data-selected] {
    background-color: var(--neutral-300);
}

.extra-network-tree div.tree-list-content:hover {
    -webkit-transition: all 0.05s ease-in-out;
	transition: all 0.05s ease-in-out;
    background-color: var(--neutral-200);
}

/* ==== CHEVRON ICON ACTIONS ==== */
/* Define the animation for the arrow when it is clicked. */
.extra-network-tree .tree-list-content-dir .tree-list-item-action-chevron {
    -ms-transform: rotate(135deg);
    -webkit-transform: rotate(135deg);
    transform: rotate(135deg);
    transition: transform 0.2s;
}

.extra-network-tree .tree-list-content-dir[data-expanded] .tree-list-item-action-chevron {
    -ms-transform: rotate(225deg);
    -webkit-transform: rotate(225deg);
    transform: rotate(225deg);
    transition: transform 0.2s;
}

.tree-list-item-action-chevron {
    display: inline-flex;
    /* Uses box shadow to generate a pseudo chevron `>` icon. */
    padding: 0.3rem;
    box-shadow: 0.1rem 0.1rem 0 0 var(--neutral-200) inset;
    transform: rotate(135deg);
}

/* ==== SEARCH INPUT ACTIONS ==== */
/* Add icon to left side of <input> */
.extra-network-control .extra-network-control--search::before {
    content: "🔎︎";
    position: absolute;
    margin: 0.5rem;
    font-size: 1rem;
    color: var(--input-placeholder-color);
}

.extra-network-control .extra-network-control--search {
    display: inline-flex;
    position: relative;
}

.extra-network-control .extra-network-control--search .extra-network-control--search-text {
    border: 1px solid var(--button-secondary-border-color);
    border-radius: 0.5rem;
    color: var(--button-secondary-text-color);
    background-color: transparent;
    width: 100%;
    padding-left: 2rem;
    line-height: 1rem;
}


.extra-network-control .extra-network-control--search .extra-network-control--search-text::placeholder {
    color: var(--input-placeholder-color);
}


/* <input> clear button (x on right side) styling */
.extra-network-control .extra-network-control--search .extra-network-control--search-text::-webkit-search-cancel-button {
    -webkit-appearance: none;
    appearance: none;
    cursor: pointer;
    height: 1rem;
    width: 1rem;
    mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="black" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>');
    mask-repeat: no-repeat;
    mask-position: center center;
    mask-size: 100%;
    background-color: var(--input-placeholder-color);
}

/* ==== SORT ICON ACTIONS ==== */
.extra-network-control .extra-network-control--sort {
    padding: 0.25rem;
    display: inline-flex;
    cursor: pointer;
    justify-self: center;
    align-self: center;
}

.extra-network-control .extra-network-control--sort .extra-network-control--sort-icon {
    height: 1.5rem;
    width: 1.5rem;
    mask-repeat: no-repeat;
    mask-position: center center;
    mask-size: 100%;
    background-color: var(--input-placeholder-color);
}

.extra-network-control .extra-network-control--sort[data-sortkey="default"] .extra-network-control--sort-icon {
    mask-image: url('data:image/svg+xml,<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path fill-rule="evenodd" clip-rule="evenodd" d="M1 5C1 3.34315 2.34315 2 4 2H8.43845C9.81505 2 11.015 2.93689 11.3489 4.27239L11.7808 6H13.5H20C21.6569 6 23 7.34315 23 9V11C23 11.5523 22.5523 12 22 12C21.4477 12 21 11.5523 21 11V9C21 8.44772 20.5523 8 20 8H13.5H11.7808H4C3.44772 8 3 8.44772 3 9V10V19C3 19.5523 3.44772 20 4 20H9C9.55228 20 10 20.4477 10 21C10 21.5523 9.55228 22 9 22H4C2.34315 22 1 20.6569 1 19V10V9V5ZM3 6.17071C3.31278 6.06015 3.64936 6 4 6H9.71922L9.40859 4.75746C9.2973 4.3123 8.89732 4 8.43845 4H4C3.44772 4 3 4.44772 3 5V6.17071ZM20.1716 18.7574C20.6951 17.967 21 17.0191 21 16C21 13.2386 18.7614 11 16 11C13.2386 11 11 13.2386 11 16C11 18.7614 13.2386 21 16 21C17.0191 21 17.967 20.6951 18.7574 20.1716L21.2929 22.7071C21.6834 23.0976 22.3166 23.0976 22.7071 22.7071C23.0976 22.3166 23.0976 21.6834 22.7071 21.2929L20.1716 18.7574ZM13 16C13 14.3431 14.3431 13 16 13C17.6569 13 19 14.3431 19 16C19 17.6569 17.6569 19 16 19C14.3431 19 13 17.6569 13 16Z" fill="%23000000"></path></g></svg>');
}

.extra-network-control .extra-network-control--sort[data-sortkey="name"] .extra-network-control--sort-icon {
    mask-image: url('data:image/svg+xml,<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path fill-rule="evenodd" clip-rule="evenodd" d="M17.1841 6.69223C17.063 6.42309 16.7953 6.25 16.5002 6.25C16.2051 6.25 15.9374 6.42309 15.8162 6.69223L11.3162 16.6922C11.1463 17.07 11.3147 17.514 11.6924 17.6839C12.0701 17.8539 12.5141 17.6855 12.6841 17.3078L14.1215 14.1136H18.8789L20.3162 17.3078C20.4862 17.6855 20.9302 17.8539 21.308 17.6839C21.6857 17.514 21.8541 17.07 21.6841 16.6922L17.1841 6.69223ZM16.5002 8.82764L14.7965 12.6136H18.2039L16.5002 8.82764Z" fill="%231C274C"></path><path opacity="0.5" fill-rule="evenodd" clip-rule="evenodd" d="M2.25 7C2.25 6.58579 2.58579 6.25 3 6.25H13C13.4142 6.25 13.75 6.58579 13.75 7C13.75 7.41421 13.4142 7.75 13 7.75H3C2.58579 7.75 2.25 7.41421 2.25 7Z" fill="%231C274C"></path><path opacity="0.5" d="M2.25 12C2.25 11.5858 2.58579 11.25 3 11.25H10C10.4142 11.25 10.75 11.5858 10.75 12C10.75 12.4142 10.4142 12.75 10 12.75H3C2.58579 12.75 2.25 12.4142 2.25 12Z" fill="%231C274C"></path><path opacity="0.5" d="M2.25 17C2.25 16.5858 2.58579 16.25 3 16.25H8C8.41421 16.25 8.75 16.5858 8.75 17C8.75 17.4142 8.41421 17.75 8 17.75H3C2.58579 17.75 2.25 17.4142 2.25 17Z" fill="%231C274C"></path></g></svg>');
}

.extra-network-control .extra-network-control--sort[data-sortkey="date_created"] .extra-network-control--sort-icon {
    mask-image: url('data:image/svg+xml,<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M17 11C14.2386 11 12 13.2386 12 16C12 18.7614 14.2386 21 17 21C19.7614 21 22 18.7614 22 16C22 13.2386 19.7614 11 17 11ZM17 11V9M2 9V15.8C2 16.9201 2 17.4802 2.21799 17.908C2.40973 18.2843 2.71569 18.5903 3.09202 18.782C3.51984 19 4.0799 19 5.2 19H13M2 9V8.2C2 7.0799 2 6.51984 2.21799 6.09202C2.40973 5.71569 2.71569 5.40973 3.09202 5.21799C3.51984 5 4.0799 5 5.2 5H13.8C14.9201 5 15.4802 5 15.908 5.21799C16.2843 5.40973 16.5903 5.71569 16.782 6.09202C17 6.51984 17 7.0799 17 8.2V9M2 9H17M5 3V5M14 3V5M15 16H17M17 16H19M17 16V14M17 16V18" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></g></svg>');
}

.extra-network-control .extra-network-control--sort[data-sortkey="date_modified"] .extra-network-control--sort-icon {
    mask-image: url('data:image/svg+xml,<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M10 21H6.2C5.0799 21 4.51984 21 4.09202 20.782C3.71569 20.5903 3.40973 20.2843 3.21799 19.908C3 19.4802 3 18.9201 3 17.8V8.2C3 7.0799 3 6.51984 3.21799 6.09202C3.40973 5.71569 3.71569 5.40973 4.09202 5.21799C4.51984 5 5.0799 5 6.2 5H17.8C18.9201 5 19.4802 5 19.908 5.21799C20.2843 5.40973 20.5903 5.71569 20.782 6.09202C21 6.51984 21 7.0799 21 8.2V10M7 3V5M17 3V5M3 9H21M13.5 13.0001L7 13M10 17.0001L7 17M14 21L16.025 20.595C16.2015 20.5597 16.2898 20.542 16.3721 20.5097C16.4452 20.4811 16.5147 20.4439 16.579 20.399C16.6516 20.3484 16.7152 20.2848 16.8426 20.1574L21 16C21.5523 15.4477 21.5523 14.5523 21 14C20.4477 13.4477 19.5523 13.4477 19 14L14.8426 18.1574C14.7152 18.2848 14.6516 18.3484 14.601 18.421C14.5561 18.4853 14.5189 18.5548 14.4903 18.6279C14.458 18.7102 14.4403 18.7985 14.405 18.975L14 21Z" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></g></svg>');
}

/* ==== SORT DIRECTION ICON ACTIONS ==== */
.extra-network-control .extra-network-control--sort-dir {
    padding: 0.25rem;
    display: inline-flex;
    cursor: pointer;
    justify-self: center;
    align-self: center;
}

.extra-network-control .extra-network-control--sort-dir .extra-network-control--sort-dir-icon {
    height: 1.5rem;
    width: 1.5rem;
    mask-repeat: no-repeat;
    mask-position: center center;
    mask-size: 100%;
    background-color: var(--input-placeholder-color);
}

.extra-network-control .extra-network-control--sort-dir[data-sortdir="Ascending"] .extra-network-control--sort-dir-icon {
    mask-image: url('data:image/svg+xml,<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M13 12H21M13 8H21M13 16H21M6 7V17M6 7L3 10M6 7L9 10" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></g></svg>');
}

.extra-network-control .extra-network-control--sort-dir[data-sortdir="Descending"] .extra-network-control--sort-dir-icon {
    mask-image: url('data:image/svg+xml,<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M13 12H21M13 8H21M13 16H21M6 7V17M6 17L3 14M6 17L9 14" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></g></svg>');
}

/* ==== TREE VIEW ICON ACTIONS ==== */
.extra-network-control .extra-network-control--tree-view {
    padding: 0.25rem;
    display: inline-flex;
    cursor: pointer;
    justify-self: center;
    align-self: center;
}

.extra-network-control .extra-network-control--tree-view .extra-network-control--tree-view-icon {
    height: 1.5rem;
    width: 1.5rem;
    mask-image: url('data:image/svg+xml,<svg viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="black"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path fill="black" d="M16 10v-4h-11v1h-2v-3h9v-4h-12v4h2v10h3v2h11v-4h-11v1h-2v-5h2v2z"></path></g></svg>');
    mask-repeat: no-repeat;
    mask-position: center center;
    mask-size: 100%;
    background-color: var(--input-placeholder-color);
}

.extra-network-control .extra-network-control--enabled {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 0.25rem;
}

.dark .extra-network-control .extra-network-control--enabled {
    background-color: rgba(255, 255, 255, 0.15);
}

.extra-network-control .extra-network-control--enabled .extra-network-control--icon{
    background-color: var(--button-secondary-text-color);
}

/* ==== REFRESH ICON ACTIONS ==== */
.extra-network-control .extra-network-control--refresh {
    padding: 0.25rem;
    display: inline-flex;
    cursor: pointer;
    justify-self: center;
    align-self: center;
}

.extra-network-control .extra-network-control--refresh .extra-network-control--refresh-icon {
    height: 1.5rem;
    width: 1.5rem;
    mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="bevel"><path d="M21.5 2v6h-6M21.34 15.57a10 10 0 1 1-.57-8.38"/></svg>');
    mask-repeat: no-repeat;
    mask-position: center center;
    mask-size: 100%;
    background-color: var(--input-placeholder-color);
}

.extra-network-control .extra-network-control--refresh-icon:active {
    -ms-transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
    transition: transform 0.2s;
}

/* ==== TREE GRID CONFIG ==== */

/* Text for button. */
.extra-network-tree .tree-list-item-label {
    position: relative;
    line-height: 1.25rem;
    color: var(--button-secondary-text-color);
    grid-area: label;
    padding-left: 0.5rem;
}

/* Text for button truncated. */
.extra-network-tree .tree-list-item-label--truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Icon for button. */
.extra-network-tree .tree-list-item-visual {
    min-height: 1rem;
    color: var(--button-secondary-text-color);
    pointer-events: none;
    align-items: right;
}


/* Icon for button when it is before label. */
.extra-network-tree .tree-list-item-visual--leading {
    grid-area: leading-visual;
    width: 1rem;
    text-align: right;
}

/* Icon for button when it is after label. */
.extra-network-tree .tree-list-item-visual--trailing {
    grid-area: trailing-visual;
    width: 1rem;
    text-align: right;
}

/* Dropdown arrow for button. */
.extra-network-tree .tree-list-item-action--leading {
    margin-right: 0.5rem;
    margin-left: 0.2rem;
}

.extra-network-tree .tree-list-content-file .tree-list-item-action--leading {
    visibility: hidden;
}

.extra-network-tree .tree-list-item-action--leading {
    grid-area: leading-action;
}

.extra-network-tree .tree-list-item-action--trailing {
    grid-area: trailing-action;
    display: inline-flex;
}

.extra-network-tree .tree-list-content .button-row {
    display: inline-flex;
    visibility: hidden;
    color: var(--button-secondary-text-color);
    width: 0;
}

.extra-network-tree .tree-list-content:hover .button-row {
    visibility: visible;
    width: auto;
}
